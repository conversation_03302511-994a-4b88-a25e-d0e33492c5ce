apiVersion: apps/v1
kind: Deployment
metadata:
  name: ctint-visual-tracking-platform-deployment
  namespace: cdss-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ctint-visual-tracking-platform
  template:
    metadata:
      labels:
        app: ctint-visual-tracking-platform
    spec:
      # 初始化容器，确保目录权限正确
      initContainers:
        - name: init-permissions
          image: busybox:1.35
          command: ['sh', '-c']
          args:
            - |
              echo "Creating directories and setting permissions..."
              mkdir -p /app/log /app/data
              chmod 755 /app/log /app/data
              echo "Directories created successfully"
          volumeMounts:
            - name: pagespy-log-storage
              mountPath: /app/log
              subPath: ctint-visual-tracking-platform/log
            - name: pagespy-data-storage
              mountPath: /app/data
              subPath: ctint-visual-tracking-platform/data
      containers:
        - name: ctint-visual-tracking-platform
          image: cdss3uatacr.azurecr.io/ctint-visual-tracking-platform:1
          ports:
            - name: http
              containerPort: 6752
              protocol: TCP
          # PageSpy 离线日志目录映射到 Azure File Share
          volumeMounts:
            - name: pagespy-log-storage
              mountPath: /app/log
              subPath: ctint-visual-tracking-platform/log
      # PageSpy Azure File Share 存储卷
      volumes:
        - name: pagespy-log-storage
          azureFile:
            secretName: ctintcdss3uatakssa-share
            shareName: ctint-cdss-logs
            readOnly: false