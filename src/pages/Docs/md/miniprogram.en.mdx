
import { Tooltip } from 'antd'
import mpPanelImg from '@/assets/image/screenshot/mp-panel.png'

### Step 1#step-1

Install the dependencies in the project. We provide SDKs for several mini-program platforms. Please install according to your needs:

* Wechat Miniprogram
```bash
yarn add @huolala-tech/page-spy-wechat@latest
```
* Alipay Miniprogram
```bash
yarn add @huolala-tech/page-spy-alipay@latest
```
* UniAPP
```bash
yarn add @huolala-tech/page-spy-uniapp@latest
```
* Taro
```bash
yarn add @huolala-tech/page-spy-taro@latest
```

### Step 2#step-2

Add the PageSpy service domain name to the whitelist of http and websocket requests in the mini-program. Note that except for the development environment, the mini-program requires the use of https and wss protocols:

```
https://<your-pagespy-host>
wss://<your-pagespy-host>
```

### Step 3#step-3

Import the SDK in the entry file and instantiate it. The initialization parameters provide optional [configuration](./pagespy#constructor) to customize the behavior of the SDK:

```js
import PageSpy from '@huolala-tech/page-spy-wechat';

const $pageSpy = new PageSpy({
  api: "{deployPath}",
})
```

### Debugging panel#debugging

Call the `showPanel()` method on the created PageSpy instance to pop up a debugging menu for assistance with debugging:

<img src={mpPanelImg} style={{ maxWidth: 320 }} />

<br/>

That's the complete process to integrate PageSpy into a mini-program project.
