.console-node {
  margin-right: 7px;
  word-break: break-word;
  &.origin {
    color: #323941;
  }
  &.number {
    color: #2323f5;
  }
  &.string {
    color: #b82519;
  }
  &.boolean {
    color: #14229f;
  }
  &.symbol {
    color: #b82519;
  }
  &.error {
    color: #eb3223;
  }
  &.function {
    color: #4e360e;
  }
  &.object {
    color: #6a6b6d;
    cursor: pointer;
    white-space: nowrap;
  }
  &.null,
  &.undefined {
    color: #767b7f;
  }
  &.bigint {
    color: #396b2f;
  }
  .right-mustache {
    margin-left: -6px;
  }
}

.atom-node {
  cursor: default;
  &.disabled {
    color: #8c8c8c;
    cursor: not-allowed;
  }
  .spread-controller {
    transform: rotateZ(0);
    transition: transform linear 0.1s;
    &.spread {
      transform: rotateZ(90deg);
    }
  }
}

.atom-node:not(.atom-node .atom-node) > .property-panel {
  margin-left: 20px;
}

.property-panel {
  margin-left: 12px;
  .spread-controller {
    margin-left: -12px;
  }
  .property-key {
    color: #8f198d;
    font-weight: 700;
  }
  .property-value {
    &.ellipsis {
      font-style: normal;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }
    .copyable-content:hover {
      background-color: #eee;
    }
  }
  .load-more-btn {
    color: #8f198d;
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
}
