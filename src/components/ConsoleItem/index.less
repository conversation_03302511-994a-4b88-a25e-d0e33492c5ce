.console-item {
  display: flex;
  // align-items: center;
  border-top: 1px solid #f0f0f0;
  padding: 4px 8px;
  font-size: 12px;
  white-space: pre-wrap;
  &:first-child {
    border-top: none;
  }
  &:last-child {
    border-bottom: 1px solid #f0f0f0;
  }
  &.debug {
    color: #1c419a;
  }
  &.warn {
    color: #573c10;
    background-color: #fefae3;
    border-color: #fdf4be;
  }
  &.error {
    color: #eb3223;
    background-color: #fdeeee;
    border-color: #f9d2d1;
    // color: #ec301d;
  }
  &__title {
    margin-top: 2px;
  }
  &__content {
    flex: 1;
  }
  &__url {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #666666;
    text-decoration: underline;
    transition: all ease-out 0.3s;
    &:hover {
      color: #000;
    }
  }
  .rjv {
    font-size: 12px;
    svg {
      &.rjv-ref-arrow {
        width: 8px;
        height: 8px;
      }
    }
  }
  .non-serializable {
    font-size: 12px;
    color: #aaa;
  }
}
