.error-message-box {
  padding: 8px 12px;
  overflow: auto;
  border-radius: 4px;
  color: #eb3223;
  background-color: #fdeeee;
  line-height: 1.4;
}
.error-stack-item {
  & ~ & {
    margin-top: 12px;
  }
  .source-code-fragments {
    position: relative;
    .fragments-header {
      position: absolute;
      left: 12px;
      right: 12px;
      top: 10px;
    }
    .origin-filename {
      font-size: 14px;
      line-height: 32px;
      color: #aaa;
    }
    pre {
      padding: 50px 12px 8px;
      border-radius: 4px;
      overflow: auto;
    }
    code {
      counter-reset: step;
      counter-increment: step calc(var(--start, 1) - 1);
      .line {
        color: rgba(182, 196, 200, 0.507);
        opacity: 0.5;
        &::before {
          content: counter(step);
          counter-increment: step;
          width: 1rem;
          margin-right: 1rem;
          display: inline-block;
          text-align: right;
        }
        &:nth-child(5) {
          opacity: 1;
          &::before {
            color: #eee;
            font-weight: 700;
          }
        }
      }
      .error-line {
        display: inline-block;
        margin-bottom: 1em;
        position: relative;
        font-size: 14px;
        &::after {
          content: '^';
          color: #f97583;
          position: absolute;
          left: calc(2rem + var(--position) * 1ch);
          top: 100%;
        }
      }
    }
  }
  @keyframes fade {
    0% {
      opacity: 1;
    }
    100% {
      opacity: 0.3;
    }
  }
  .locate-icon {
    font-size: 18px;
    color: @primary-color;
    &.loading {
      animation: fade 1s linear infinite alternate;
    }
  }
}
