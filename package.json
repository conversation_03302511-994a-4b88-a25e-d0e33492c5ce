{"private": true, "version": "2.0.0", "description": "All-In-One Remote Debugging Tool", "homepage": "https://huolalatech.github.io/page-spy-web", "repository": {"type": "git", "url": "git+https://github.com/HuolalaTech/page-spy-web.git"}, "license": "MIT", "scripts": {"postinstall": "bash scripts/public-files.sh", "start:doc": "vite --mode doc", "start:client": "vite --mode client", "start:server": "page-spy-api", "build:doc": "vite build --mode doc", "build:client": "vite build --mode client", "preview": "vite preview", "lint": "eslint --ext .js,.jsx,.ts,.tsx ./src", "preversion": "bash scripts/check-branch.sh", "postversion": "bash scripts/check-changelog.sh", "docker:build": "docker build -t ctint-visual-tracking-platform .", "docker:run": "docker run -p 6752:6752 ctint-visual-tracking-platform"}, "devDependencies": {"@babel/eslint-parser": "^7.19.1", "@mdx-js/rollup": "^3.0.1", "@rrweb/types": "^2.0.0-alpha.11", "@types/lodash-es": "^4.17.7", "@types/mdx": "^2.0.4", "@types/node": "^17.0.16", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-resizable": "^3.0.4", "@types/react-transition-group": "^4.4.5", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-basic-ssl": "^1.0.1", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.2", "eslint": "^8.34.0", "eslint-config-alloy": "^4.9.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "less": "^4.1.2", "lint-staged": "^12.3.3", "prettier": "^2.5.1", "remark-gfm": "^4.0.0", "typescript": "^5.3.3", "vite": "^5.4.11", "vite-plugin-svgr": "^2.4.0", "yorkie": "^2.0.0"}, "dependencies": {"@ant-design/icons": "^4.7.0", "@huolala-tech/page-spy-browser": "^2.0.0", "@huolala-tech/page-spy-plugin-data-harbor": "^2.0.0", "@huolala-tech/page-spy-plugin-rrweb": "^2.0.0", "@huolala-tech/page-spy-plugin-whole-bundle": "^2.0.0", "@huolala-tech/page-spy-types": "^2.0.0", "@huolala-tech/react-json-view": "^1.2.5", "@huolala-tech/request": "^1.1.2", "@mdx-js/mdx": "^3.0.1", "acorn": "^8.11.3", "ahooks": "^3.1.9", "antd": "^5.18.3", "clsx": "^1.2.1", "copy-to-clipboard": "^3.3.3", "dayjs": "1.11.7", "error-stack-parser": "^2.1.4", "fflate": "^0.8.1", "i18next": "^22.4.10", "i18next-browser-languagedetector": "^7.0.1", "immer": "^10.0.1", "lodash-es": "^4.17.21", "pretty-bytes": "^6.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^12.2.0", "react-resizable": "^3.0.5", "react-router-dom": "^6.2.1", "react-transition-group": "^4.4.5", "rehype-parse": "^9.0.0", "rehype-stringify": "^10.0.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.0", "rrweb-player": "^1.0.0-alpha.4", "shiki": "^0.14.3", "source-map": "^0.7.4", "unified": "^11.0.5", "unist-util-visit": "^4.1.2", "zustand": "^4.5.2"}, "resolutions": {"rollup": "npm:@rollup/wasm-node"}, "gitHooks": {"pre-commit": "lint-staged && node ./scripts/check-i18n.mjs"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "browserslist": ["last 2 chrome version", "last 2 firefox version", "last 2 safari version"], "optionalDependencies": {"@huolala-tech/page-spy-api": "^1.9.0"}}