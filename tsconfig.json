{"compilerOptions": {"target": "esnext", "module": "esnext", "allowJs": true, "isolatedModules": true, "moduleResolution": "node", "importHelpers": true, "jsx": "react-jsx", "resolveJsonModule": true, "esModuleInterop": true, "sourceMap": true, "baseUrl": "./", "strict": true, "types": ["vite-plugin-svgr/client", "mdx"], "paths": {"@/*": ["src/*"]}, "allowSyntheticDefaultImports": true, "useDefineForClassFields": false}, "include": ["src/**/*"]}