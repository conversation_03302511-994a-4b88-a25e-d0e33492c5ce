<div align="center">
  <img src="./doc/assets/cdss-logo.svg" height="100" />

  CTINT前端可视化Web调试平台

</div>

## 简介

**CTINT前端可视化Web调试平台** 是基于 **PageSpy** 的调试 Web 项目的工具。

支持以下功能:
- 远程实时调试
- 离线日志回放
- 离线日志导出/导入

## 什么是 PageSpy?

> 一图胜千言。

![Why is PageSpy](./doc/assets/why-is-pagespy-zh.png)

**CTINT前端可视化Web调试平台** 主要由三个部分组成：

- 调试端 WebUI: <code>src</code> 目录下；
- 服务器端: <code>backend</code> 目录下 ；
- 需要在客户端引入的 SDK 代码在 HuolalaTech/page-spy 仓库维护；

> 当前仓库是从**HuolalaTech/page-spy-web**中fork出来的。仓库中包含了前端和后端的代码。


## 架构原理
![Architecture](./doc/assets/interaction.png)

## 本地启动

1. 在项目<code>.env.client</code>中配置前端请求API的<strong>base url</strong>
```javascript
// 后端启动默认端口为 6752, 可通过config.json来配置
VITE_API_BASE=localhost:6752
```

2. 启动后端项目
```shell
# 启动本地后端服务
$ npm run start:server #默认端口为 6752
```

3. 启动前端项目
```shell
# 安装需要的npm依赖
$ npm install

# 启动本地前端服务
$ npm run start:client # 启动成功后，访问 http://localhost:5173/tracking/
```

## CTINT UAT
URL：https://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com/tracking/

## 项目打包

>已经使用了Azure Devops构建 [Docker image](https://dev.azure.com/ctint-product-development/ctint-microfrontends/_build?definitionId=69)

1. 使用<code>Dockerfile</code>进行打包
```docker
# 构建前端静态资源
FROM node:20.16.0 AS frontend
WORKDIR /app
COPY . .
RUN yarn install
RUN yarn build:client

# 将构建完的前端资源放入后端目录，并一起打包
FROM golang:1.20-buster AS backend
WORKDIR /app
COPY backend/go.mod backend/go.sum ./
RUN go mod download
COPY backend/. .
COPY --from=frontend /app/dist ./dist
RUN go build -o main .

# 启动项目
FROM debian:bullseye-slim
WORKDIR /app
COPY --from=backend /app/main /app/main
COPY LICENSE LICENSE
CMD ["/app/main"]
```

2. 构建步骤：
* 构建前端静态资源（html, js, css, etc.）并打包到<code>dist</code>目录

* 将<code>dist</code>目录拷贝到<code>backend</code>目录，因为后端项目会启动一个代理，将前端静态资源进行代理，从而实现一个pod同时包含前后端项目

* 后端使用<code>github.com/HuolalaTech/page-spy-api</code>库，直接启动一个后端服务，可通过config.json进行后端配置，最后通过<code>go build -o main .</code>进行打包

## 使用
1. 客户端集成：https://www.pagespy.org/#/docs/browser

2. SDK接口：https://www.pagespy.org/#/docs/api

3. 离线日志回放：https://www.pagespy.org/#/docs/offline-log